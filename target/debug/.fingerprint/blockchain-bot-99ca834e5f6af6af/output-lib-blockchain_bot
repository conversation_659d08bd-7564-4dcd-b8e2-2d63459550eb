{"$message_type":"diagnostic","message":"unused import: `commitment_config::CommitmentConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/production_jito.rs","byte_start":700,"byte_end":735,"line_start":19,"line_end":19,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    commitment_config::CommitmentConfig,","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/production_jito.rs","byte_start":694,"byte_end":735,"line_start":18,"line_end":19,"column_start":15,"column_end":40,"is_primary":true,"text":[{"text":"    hash::Hash,","highlight_start":15,"highlight_end":16},{"text":"    commitment_config::CommitmentConfig,","highlight_start":1,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `commitment_config::CommitmentConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/production_jito.rs:19:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    commitment_config::CommitmentConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/production_jito.rs","byte_start":898,"byte_end":918,"line_start":26,"line_end":26,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"    collections::HashMap,","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/production_jito.rs","byte_start":892,"byte_end":918,"line_start":25,"line_end":26,"column_start":54,"column_end":25,"is_primary":true,"text":[{"text":"    time::{SystemTime, UNIX_EPOCH, Duration, Instant},","highlight_start":54,"highlight_end":55},{"text":"    collections::HashMap,","highlight_start":1,"highlight_end":25}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/production_jito.rs","byte_start":837,"byte_end":843,"line_start":24,"line_end":25,"column_start":10,"column_end":5,"is_primary":true,"text":[{"text":"use std::{","highlight_start":10,"highlight_end":11},{"text":"    time::{SystemTime, UNIX_EPOCH, Duration, Instant},","highlight_start":1,"highlight_end":5}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/production_jito.rs","byte_start":918,"byte_end":921,"line_start":26,"line_end":27,"column_start":25,"column_end":2,"is_primary":true,"text":[{"text":"    collections::HashMap,","highlight_start":25,"highlight_end":26},{"text":"};","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `collections::HashMap`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/production_jito.rs:26:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    collections::HashMap,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow::Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/periodic_scanner.rs","byte_start":4,"byte_end":18,"line_start":1,"line_end":1,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/periodic_scanner.rs","byte_start":0,"byte_end":20,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":1,"highlight_end":20},{"text":"use std::time::{Duration, Instant};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `anyhow::Result`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/periodic_scanner.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::Result;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `TransactionResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":435,"byte_end":452,"line_start":9,"line_end":9,"column_start":59,"column_end":76,"is_primary":true,"text":[{"text":"use crate::priority_fees_executor::{PriorityFeesExecutor, TransactionResult};","highlight_start":59,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":433,"byte_end":452,"line_start":9,"line_end":9,"column_start":57,"column_end":76,"is_primary":true,"text":[{"text":"use crate::priority_fees_executor::{PriorityFeesExecutor, TransactionResult};","highlight_start":57,"highlight_end":76}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":412,"byte_end":413,"line_start":9,"line_end":9,"column_start":36,"column_end":37,"is_primary":true,"text":[{"text":"use crate::priority_fees_executor::{PriorityFeesExecutor, TransactionResult};","highlight_start":36,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":452,"byte_end":453,"line_start":9,"line_end":9,"column_start":76,"column_end":77,"is_primary":true,"text":[{"text":"use crate::priority_fees_executor::{PriorityFeesExecutor, TransactionResult};","highlight_start":76,"highlight_end":77}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `TransactionResult`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/trade_executor.rs:9:59\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::priority_fees_executor::{PriorityFeesExecutor, TransactionResult};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `ManagedPosition`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/position_monitor.rs","byte_start":93,"byte_end":108,"line_start":4,"line_end":4,"column_start":48,"column_end":63,"is_primary":true,"text":[{"text":"use crate::position_manager::{PositionManager, ManagedPosition};","highlight_start":48,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/position_monitor.rs","byte_start":91,"byte_end":108,"line_start":4,"line_end":4,"column_start":46,"column_end":63,"is_primary":true,"text":[{"text":"use crate::position_manager::{PositionManager, ManagedPosition};","highlight_start":46,"highlight_end":63}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/position_monitor.rs","byte_start":75,"byte_end":76,"line_start":4,"line_end":4,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"use crate::position_manager::{PositionManager, ManagedPosition};","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/position_monitor.rs","byte_start":108,"byte_end":109,"line_start":4,"line_end":4,"column_start":63,"column_end":64,"is_primary":true,"text":[{"text":"use crate::position_manager::{PositionManager, ManagedPosition};","highlight_start":63,"highlight_end":64}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `ManagedPosition`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/position_monitor.rs:4:48\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::position_manager::{PositionManager, ManagedPosition};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow::Result`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/sell_logic.rs","byte_start":4,"byte_end":18,"line_start":1,"line_end":1,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/sell_logic.rs","byte_start":0,"byte_end":20,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::Result;","highlight_start":1,"highlight_end":20},{"text":"use std::time::Duration;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `anyhow::Result`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/sell_logic.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::Result;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `SystemTime` and `UNIX_EPOCH`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":46,"byte_end":56,"line_start":2,"line_end":2,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"use std::time::{SystemTime, UNIX_EPOCH, Duration, Instant};","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":58,"byte_end":68,"line_start":2,"line_end":2,"column_start":29,"column_end":39,"is_primary":true,"text":[{"text":"use std::time::{SystemTime, UNIX_EPOCH, Duration, Instant};","highlight_start":29,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":46,"byte_end":70,"line_start":2,"line_end":2,"column_start":17,"column_end":41,"is_primary":true,"text":[{"text":"use std::time::{SystemTime, UNIX_EPOCH, Duration, Instant};","highlight_start":17,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `SystemTime` and `UNIX_EPOCH`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/dip_monitor.rs:2:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{SystemTime, UNIX_EPOCH, Duration, Instant};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::time::sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":222,"byte_end":240,"line_start":7,"line_end":7,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":218,"byte_end":242,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `tokio::time::sleep`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/dip_monitor.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::time::sleep;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `TradingConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":298,"byte_end":311,"line_start":9,"line_end":9,"column_start":56,"column_end":69,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":56,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":296,"byte_end":311,"line_start":9,"line_end":9,"column_start":54,"column_end":69,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":54,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":282,"byte_end":283,"line_start":9,"line_end":9,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/dip_monitor.rs","byte_start":311,"byte_end":312,"line_start":9,"line_end":9,"column_start":69,"column_end":70,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":69,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `TradingConfig`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/dip_monitor.rs:9:56\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::live_pump_integration_main::{LiveTokenData, TradingConfig};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `account::Account` and `commitment_config::CommitmentConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":334,"byte_end":369,"line_start":10,"line_end":10,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    commitment_config::CommitmentConfig,","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":375,"byte_end":391,"line_start":11,"line_end":11,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    account::Account,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":328,"byte_end":391,"line_start":9,"line_end":11,"column_start":19,"column_end":21,"is_primary":true,"text":[{"text":"    pubkey::Pubkey,","highlight_start":19,"highlight_end":20},{"text":"    commitment_config::CommitmentConfig,","highlight_start":1,"highlight_end":41},{"text":"    account::Account,","highlight_start":1,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `account::Account` and `commitment_config::CommitmentConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    commitment_config::CommitmentConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    account::Account,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::time::sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":578,"byte_end":596,"line_start":20,"line_end":20,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":574,"byte_end":598,"line_start":20,"line_end":21,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":1,"highlight_end":24},{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `tokio::time::sleep`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:20:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::time::sleep;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Deserialize` and `Serialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":610,"byte_end":621,"line_start":21,"line_end":21,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":623,"byte_end":632,"line_start":21,"line_end":21,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":598,"byte_end":635,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":37},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Deserialize` and `Serialize`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:21:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `TradingConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":691,"byte_end":704,"line_start":23,"line_end":23,"column_start":56,"column_end":69,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":56,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":689,"byte_end":704,"line_start":23,"line_end":23,"column_start":54,"column_end":69,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":54,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":675,"byte_end":676,"line_start":23,"line_end":23,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":704,"byte_end":705,"line_start":23,"line_end":23,"column_start":69,"column_end":70,"is_primary":true,"text":[{"text":"use crate::live_pump_integration_main::{LiveTokenData, TradingConfig};","highlight_start":69,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `TradingConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:23:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::live_pump_integration_main::{LiveTokenData, TradingConfig};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::pump_api_client::PumpApiClient`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":711,"byte_end":748,"line_start":24,"line_end":24,"column_start":5,"column_end":42,"is_primary":true,"text":[{"text":"use crate::pump_api_client::PumpApiClient;","highlight_start":5,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":707,"byte_end":750,"line_start":24,"line_end":25,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::pump_api_client::PumpApiClient;","highlight_start":1,"highlight_end":43},{"text":"use crate::chainstack_client::ChainstackRpcClient;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::pump_api_client::PumpApiClient`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:24:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::pump_api_client::PumpApiClient;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `crate::logs_event_processor::TokenCreationInfo`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1075,"byte_end":1121,"line_start":31,"line_end":31,"column_start":5,"column_end":51,"is_primary":true,"text":[{"text":"use crate::logs_event_processor::TokenCreationInfo;","highlight_start":5,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1071,"byte_end":1123,"line_start":31,"line_end":32,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::logs_event_processor::TokenCreationInfo;","highlight_start":1,"highlight_end":52},{"text":"use crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `crate::logs_event_processor::TokenCreationInfo`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:31:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::logs_event_processor::TokenCreationInfo;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `GraduationCandidate`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1177,"byte_end":1196,"line_start":32,"line_end":32,"column_start":55,"column_end":74,"is_primary":true,"text":[{"text":"use crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};","highlight_start":55,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1175,"byte_end":1196,"line_start":32,"line_end":32,"column_start":53,"column_end":74,"is_primary":true,"text":[{"text":"use crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};","highlight_start":53,"highlight_end":74}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1156,"byte_end":1157,"line_start":32,"line_end":32,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":1196,"byte_end":1197,"line_start":32,"line_end":32,"column_start":74,"column_end":75,"is_primary":true,"text":[{"text":"use crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};","highlight_start":74,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `GraduationCandidate`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:32:55\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::token_aging_pipeline::{TokenAgingPipeline, GraduationCandidate};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `commitment_config::CommitmentConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":505,"byte_end":540,"line_start":15,"line_end":15,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    commitment_config::CommitmentConfig,","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":499,"byte_end":540,"line_start":14,"line_end":15,"column_start":23,"column_end":40,"is_primary":true,"text":[{"text":"    system_instruction,","highlight_start":23,"highlight_end":24},{"text":"    commitment_config::CommitmentConfig,","highlight_start":1,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `commitment_config::CommitmentConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:15:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    commitment_config::CommitmentConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Instant`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":689,"byte_end":696,"line_start":21,"line_end":21,"column_start":46,"column_end":53,"is_primary":true,"text":[{"text":"    time::{SystemTime, UNIX_EPOCH, Duration, Instant},","highlight_start":46,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":687,"byte_end":696,"line_start":21,"line_end":21,"column_start":44,"column_end":53,"is_primary":true,"text":[{"text":"    time::{SystemTime, UNIX_EPOCH, Duration, Instant},","highlight_start":44,"highlight_end":53}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Instant`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:21:46\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    time::{SystemTime, UNIX_EPOCH, Duration, Instant},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `sync::Mutex` and `time::sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":761,"byte_end":772,"line_start":27,"line_end":27,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    time::sleep,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":778,"byte_end":789,"line_start":28,"line_end":28,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    sync::Mutex,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":744,"byte_end":794,"line_start":26,"line_end":30,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::{","highlight_start":1,"highlight_end":13},{"text":"    time::sleep,","highlight_start":1,"highlight_end":17},{"text":"    sync::Mutex,","highlight_start":1,"highlight_end":17},{"text":"};","highlight_start":1,"highlight_end":3},{"text":"use rand;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `sync::Mutex` and `time::sleep`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:27:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    time::sleep,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m28\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sync::Mutex,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `rand`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":798,"byte_end":802,"line_start":30,"line_end":30,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"use rand;","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":794,"byte_end":804,"line_start":30,"line_end":31,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rand;","highlight_start":1,"highlight_end":10},{"text":"// use futures_util::{SinkExt, StreamExt}; // Disabled for performance","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `rand`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:30:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rand;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `connect_async` and `tungstenite::protocol::Message`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":899,"byte_end":912,"line_start":32,"line_end":32,"column_start":25,"column_end":38,"is_primary":true,"text":[{"text":"use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};","highlight_start":25,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":914,"byte_end":944,"line_start":32,"line_end":32,"column_start":40,"column_end":70,"is_primary":true,"text":[{"text":"use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};","highlight_start":40,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":875,"byte_end":947,"line_start":32,"line_end":33,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};","highlight_start":1,"highlight_end":72},{"text":"use crate::production_jito::{ProductionJitoClient, TradingPriority};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `connect_async` and `tungstenite::protocol::Message`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:32:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m32\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio_tungstenite::{connect_async, tungstenite::protocol::Message};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/performance_dashboard.rs","byte_start":434,"byte_end":442,"line_start":11,"line_end":11,"column_start":41,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{SystemTime, UNIX_EPOCH, Duration};","highlight_start":41,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/performance_dashboard.rs","byte_start":432,"byte_end":442,"line_start":11,"line_end":11,"column_start":39,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{SystemTime, UNIX_EPOCH, Duration};","highlight_start":39,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Duration`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/performance_dashboard.rs:11:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{SystemTime, UNIX_EPOCH, Duration};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/pump_instruction_builder.rs","byte_start":227,"byte_end":233,"line_start":6,"line_end":6,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/pump_instruction_builder.rs","byte_start":225,"byte_end":233,"line_start":6,"line_end":6,"column_start":20,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":20,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/pump_instruction_builder.rs","byte_start":218,"byte_end":219,"line_start":6,"line_end":6,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/pump_instruction_builder.rs","byte_start":233,"byte_end":234,"line_start":6,"line_end":6,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `anyhow`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/pump_instruction_builder.rs:6:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::{Result, anyhow};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `sysvar`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/pump_instruction_builder.rs","byte_start":343,"byte_end":349,"line_start":11,"line_end":11,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    sysvar,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/pump_instruction_builder.rs","byte_start":337,"byte_end":349,"line_start":10,"line_end":11,"column_start":19,"column_end":11,"is_primary":true,"text":[{"text":"    system_program,","highlight_start":19,"highlight_end":20},{"text":"    sysvar,","highlight_start":1,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `sysvar`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/pump_instruction_builder.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sysvar,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `commitment_config::CommitmentConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/priority_fees_executor.rs","byte_start":424,"byte_end":459,"line_start":13,"line_end":13,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"    commitment_config::CommitmentConfig,","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/priority_fees_executor.rs","byte_start":418,"byte_end":459,"line_start":12,"line_end":13,"column_start":45,"column_end":40,"is_primary":true,"text":[{"text":"    compute_budget::ComputeBudgetInstruction,","highlight_start":45,"highlight_end":46},{"text":"    commitment_config::CommitmentConfig,","highlight_start":1,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `commitment_config::CommitmentConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/priority_fees_executor.rs:13:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    commitment_config::CommitmentConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Result` and `anyhow`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/position_state_machine.rs","byte_start":485,"byte_end":491,"line_start":13,"line_end":13,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/position_state_machine.rs","byte_start":493,"byte_end":499,"line_start":13,"line_end":13,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/position_state_machine.rs","byte_start":472,"byte_end":502,"line_start":13,"line_end":14,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use anyhow::{Result, anyhow};","highlight_start":1,"highlight_end":30},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Result` and `anyhow`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/position_state_machine.rs:13:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::{Result, anyhow};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variant `TRAILING_ACTIVE` should have an upper camel case name","code":{"code":"non_camel_case_types","explanation":null},"level":"warning","spans":[{"file_name":"src/position_state_machine.rs","byte_start":893,"byte_end":908,"line_start":27,"line_end":27,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    TRAILING_ACTIVE {","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(non_camel_case_types)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"convert the identifier to upper camel case","code":null,"level":"help","spans":[{"file_name":"src/position_state_machine.rs","byte_start":893,"byte_end":908,"line_start":27,"line_end":27,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    TRAILING_ACTIVE {","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":"TrailingActive","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variant `TRAILING_ACTIVE` should have an upper camel case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/position_state_machine.rs:27:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    TRAILING_ACTIVE {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to upper camel case: `TrailingActive`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(non_camel_case_types)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variant `SELL_PENDING` should have an upper camel case name","code":{"code":"non_camel_case_types","explanation":null},"level":"warning","spans":[{"file_name":"src/position_state_machine.rs","byte_start":1270,"byte_end":1282,"line_start":39,"line_end":39,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    SELL_PENDING {","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to upper camel case","code":null,"level":"help","spans":[{"file_name":"src/position_state_machine.rs","byte_start":1270,"byte_end":1282,"line_start":39,"line_end":39,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    SELL_PENDING {","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":"SellPending","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variant `SELL_PENDING` should have an upper camel case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/position_state_machine.rs:39:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    SELL_PENDING {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to upper camel case: `SellPending`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variant `SELL_EXECUTING` should have an upper camel case name","code":{"code":"non_camel_case_types","explanation":null},"level":"warning","spans":[{"file_name":"src/position_state_machine.rs","byte_start":1518,"byte_end":1532,"line_start":48,"line_end":48,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    SELL_EXECUTING {","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"convert the identifier to upper camel case","code":null,"level":"help","spans":[{"file_name":"src/position_state_machine.rs","byte_start":1518,"byte_end":1532,"line_start":48,"line_end":48,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    SELL_EXECUTING {","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":"SellExecuting","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variant `SELL_EXECUTING` should have an upper camel case name\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/position_state_machine.rs:48:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    SELL_EXECUTING {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: convert the identifier to upper camel case: `SellExecuting`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `json`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/chainstack_platform_client.rs","byte_start":246,"byte_end":250,"line_start":8,"line_end":8,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/chainstack_platform_client.rs","byte_start":246,"byte_end":252,"line_start":8,"line_end":8,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":18,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/chainstack_platform_client.rs","byte_start":245,"byte_end":246,"line_start":8,"line_end":8,"column_start":17,"column_end":18,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":17,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/chainstack_platform_client.rs","byte_start":257,"byte_end":258,"line_start":8,"line_end":8,"column_start":29,"column_end":30,"is_primary":true,"text":[{"text":"use serde_json::{json, Value};","highlight_start":29,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `json`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/chainstack_platform_client.rs:8:18\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde_json::{json, Value};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Deserialize` and `Serialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/shyft_client.rs","byte_start":364,"byte_end":375,"line_start":8,"line_end":8,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/shyft_client.rs","byte_start":377,"byte_end":386,"line_start":8,"line_end":8,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/shyft_client.rs","byte_start":352,"byte_end":389,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":37},{"text":"use solana_sdk::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Deserialize` and `Serialize`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/shyft_client.rs:8:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Engine as _` and `engine::general_purpose`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/shyft_client.rs","byte_start":488,"byte_end":499,"line_start":14,"line_end":14,"column_start":14,"column_end":25,"is_primary":true,"text":[{"text":"use base64::{Engine as _, engine::general_purpose};","highlight_start":14,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/shyft_client.rs","byte_start":501,"byte_end":524,"line_start":14,"line_end":14,"column_start":27,"column_end":50,"is_primary":true,"text":[{"text":"use base64::{Engine as _, engine::general_purpose};","highlight_start":27,"highlight_end":50}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/shyft_client.rs","byte_start":475,"byte_end":527,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use base64::{Engine as _, engine::general_purpose};","highlight_start":1,"highlight_end":52},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Engine as _` and `engine::general_purpose`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/shyft_client.rs:14:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse base64::{Engine as _, engine::general_purpose};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":176,"byte_end":181,"line_start":6,"line_end":6,"column_start":19,"column_end":24,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, timeout};","highlight_start":19,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":176,"byte_end":183,"line_start":6,"line_end":6,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, timeout};","highlight_start":19,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/logs_listener.rs","byte_start":175,"byte_end":176,"line_start":6,"line_end":6,"column_start":18,"column_end":19,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, timeout};","highlight_start":18,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/logs_listener.rs","byte_start":190,"byte_end":191,"line_start":6,"line_end":6,"column_start":33,"column_end":34,"is_primary":true,"text":[{"text":"use tokio::time::{sleep, timeout};","highlight_start":33,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `sleep`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:6:19\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::time::{sleep, timeout};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `anyhow`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/websocket_tester.rs","byte_start":13,"byte_end":19,"line_start":1,"line_end":1,"column_start":14,"column_end":20,"is_primary":true,"text":[{"text":"use anyhow::{anyhow, Result};","highlight_start":14,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/websocket_tester.rs","byte_start":13,"byte_end":21,"line_start":1,"line_end":1,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"use anyhow::{anyhow, Result};","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/websocket_tester.rs","byte_start":12,"byte_end":13,"line_start":1,"line_end":1,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use anyhow::{anyhow, Result};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/websocket_tester.rs","byte_start":27,"byte_end":28,"line_start":1,"line_end":1,"column_start":28,"column_end":29,"is_primary":true,"text":[{"text":"use anyhow::{anyhow, Result};","highlight_start":28,"highlight_end":29}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `anyhow`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/websocket_tester.rs:1:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse anyhow::{anyhow, Result};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Duration`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/transaction_timing.rs","byte_start":247,"byte_end":255,"line_start":7,"line_end":7,"column_start":17,"column_end":25,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":17,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/transaction_timing.rs","byte_start":247,"byte_end":257,"line_start":7,"line_end":7,"column_start":17,"column_end":27,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":17,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/transaction_timing.rs","byte_start":246,"byte_end":247,"line_start":7,"line_end":7,"column_start":16,"column_end":17,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":16,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/transaction_timing.rs","byte_start":264,"byte_end":265,"line_start":7,"line_end":7,"column_start":34,"column_end":35,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant};","highlight_start":34,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Duration`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/transaction_timing.rs:7:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{Duration, Instant};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `DexScreenerTrade`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/external_dashboard.rs","byte_start":480,"byte_end":496,"line_start":13,"line_end":13,"column_start":74,"column_end":90,"is_primary":true,"text":[{"text":"use crate::dexscreener_client::{DexScreenerClient, WalletTradingSummary, DexScreenerTrade};","highlight_start":74,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/external_dashboard.rs","byte_start":478,"byte_end":496,"line_start":13,"line_end":13,"column_start":72,"column_end":90,"is_primary":true,"text":[{"text":"use crate::dexscreener_client::{DexScreenerClient, WalletTradingSummary, DexScreenerTrade};","highlight_start":72,"highlight_end":90}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `DexScreenerTrade`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/external_dashboard.rs:13:74\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::dexscreener_client::{DexScreenerClient, WalletTradingSummary, DexScreenerTrade};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::net::TcpListener`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/enhanced_dashboard.rs","byte_start":397,"byte_end":420,"line_start":11,"line_end":11,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use tokio::net::TcpListener;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/enhanced_dashboard.rs","byte_start":393,"byte_end":422,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::net::TcpListener;","highlight_start":1,"highlight_end":29},{"text":"use warp::Filter;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `tokio::net::TcpListener`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/enhanced_dashboard.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::net::TcpListener;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `tx_result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":13447,"byte_end":13456,"line_start":290,"line_end":290,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                Ok(tx_result) => {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":13447,"byte_end":13456,"line_start":290,"line_end":290,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                Ok(tx_result) => {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":"_tx_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `tx_result`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/trade_executor.rs:290:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Ok(tx_result) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_tx_result`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `tx_result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":20522,"byte_end":20531,"line_start":434,"line_end":434,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                Ok(tx_result) => {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":20522,"byte_end":20531,"line_start":434,"line_end":434,"column_start":20,"column_end":29,"is_primary":true,"text":[{"text":"                Ok(tx_result) => {","highlight_start":20,"highlight_end":29}],"label":null,"suggested_replacement":"_tx_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `tx_result`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/trade_executor.rs:434:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m434\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Ok(tx_result) => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_tx_result`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `token_tx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":29507,"byte_end":29515,"line_start":712,"line_end":712,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"        let (token_tx, mut token_rx) = mpsc::channel::<TokenCreationEvent>(100);","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":29507,"byte_end":29515,"line_start":712,"line_end":712,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"        let (token_tx, mut token_rx) = mpsc::channel::<TokenCreationEvent>(100);","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":"_token_tx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `token_tx`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:712:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m712\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let (token_tx, mut token_rx) = mpsc::channel::<TokenCreationEvent>(100);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_token_tx`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `trade_tx`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":29588,"byte_end":29596,"line_start":713,"line_end":713,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"        let (trade_tx, mut trade_rx) = mpsc::channel::<TradeEvent>(1000);","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":29588,"byte_end":29596,"line_start":713,"line_end":713,"column_start":14,"column_end":22,"is_primary":true,"text":[{"text":"        let (trade_tx, mut trade_rx) = mpsc::channel::<TradeEvent>(1000);","highlight_start":14,"highlight_end":22}],"label":null,"suggested_replacement":"_trade_tx","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `trade_tx`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:713:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m713\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let (trade_tx, mut trade_rx) = mpsc::channel::<TradeEvent>(1000);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_trade_tx`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `progress`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":68873,"byte_end":68881,"line_start":1489,"line_end":1489,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let progress = 0.0; // New tokens start at 0% progress","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":68873,"byte_end":68881,"line_start":1489,"line_end":1489,"column_start":13,"column_end":21,"is_primary":true,"text":[{"text":"        let progress = 0.0; // New tokens start at 0% progress","highlight_start":13,"highlight_end":21}],"label":null,"suggested_replacement":"_progress","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `progress`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:1489:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1489\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let progress = 0.0; // New tokens start at 0% progress\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_progress`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `sol_reserves`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":68936,"byte_end":68948,"line_start":1490,"line_end":1490,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let sol_reserves = token_event.liquidity_sol;","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":68936,"byte_end":68948,"line_start":1490,"line_end":1490,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let sol_reserves = token_event.liquidity_sol;","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_sol_reserves","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `sol_reserves`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:1490:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1490\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let sol_reserves = token_event.liquidity_sol;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_sol_reserves`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `market_cap`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":68990,"byte_end":69000,"line_start":1491,"line_end":1491,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let market_cap = token_event.market_cap_sol * self.trading_config.sol_to_usd_rate;","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":68990,"byte_end":69000,"line_start":1491,"line_end":1491,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let market_cap = token_event.market_cap_sol * self.trading_config.sol_to_usd_rate;","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_market_cap","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `market_cap`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:1491:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1491\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let market_cap = token_event.market_cap_sol * self.trading_config.sol_to_usd_rate;\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_market_cap`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `status`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":89668,"byte_end":89674,"line_start":1900,"line_end":1900,"column_start":25,"column_end":31,"is_primary":true,"text":[{"text":"                    let status = match exit_reason {","highlight_start":25,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":89668,"byte_end":89674,"line_start":1900,"line_end":1900,"column_start":25,"column_end":31,"is_primary":true,"text":[{"text":"                    let status = match exit_reason {","highlight_start":25,"highlight_end":31}],"label":null,"suggested_replacement":"_status","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `status`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:1900:25\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1900\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let status = match exit_reason {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_status`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `base_delay`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":95974,"byte_end":95984,"line_start":2030,"line_end":2030,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let base_delay = tokio::time::Duration::from_secs(10); // Start with 10 second delays","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":95974,"byte_end":95984,"line_start":2030,"line_end":2030,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"        let base_delay = tokio::time::Duration::from_secs(10); // Start with 10 second delays","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":"_base_delay","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `base_delay`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:2030:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2030\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let base_delay = tokio::time::Duration::from_secs(10); // Start with 10 second delays\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_base_delay`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `signature`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/priority_fees_executor.rs","byte_start":11682,"byte_end":11691,"line_start":293,"line_end":293,"column_start":49,"column_end":58,"is_primary":true,"text":[{"text":"    pub async fn get_transaction_details(&self, signature: &Signature) -> Result<Option<solana_transaction_status::UiTransactionEncoding>> {","highlight_start":49,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/priority_fees_executor.rs","byte_start":11682,"byte_end":11691,"line_start":293,"line_end":293,"column_start":49,"column_end":58,"is_primary":true,"text":[{"text":"    pub async fn get_transaction_details(&self, signature: &Signature) -> Result<Option<solana_transaction_status::UiTransactionEncoding>> {","highlight_start":49,"highlight_end":58}],"label":null,"suggested_replacement":"_signature","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `signature`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/priority_fees_executor.rs:293:49\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_transaction_details(&self, signature: &Signature) -> Result<Option<solana_transaction_status::UiTransactionEncoding>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_signature`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `result`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/chainstack_client.rs","byte_start":5659,"byte_end":5665,"line_start":154,"line_end":154,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"        let result = response_json.get(\"result\")","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/chainstack_client.rs","byte_start":5659,"byte_end":5665,"line_start":154,"line_end":154,"column_start":13,"column_end":19,"is_primary":true,"text":[{"text":"        let result = response_json.get(\"result\")","highlight_start":13,"highlight_end":19}],"label":null,"suggested_replacement":"_result","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `result`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/chainstack_client.rs:154:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m154\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let result = response_json.get(\"result\")\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_result`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `current_time`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":8410,"byte_end":8422,"line_start":208,"line_end":208,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let current_time = std::time::SystemTime::now()","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":8410,"byte_end":8422,"line_start":208,"line_end":208,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let current_time = std::time::SystemTime::now()","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_current_time","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `current_time`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:208:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m208\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let current_time = std::time::SystemTime::now()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_current_time`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `min_age_seconds`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":9618,"byte_end":9633,"line_start":236,"line_end":236,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        min_age_seconds: u64,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":9618,"byte_end":9633,"line_start":236,"line_end":236,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        min_age_seconds: u64,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"_min_age_seconds","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `min_age_seconds`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:236:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m236\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        min_age_seconds: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_min_age_seconds`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `max_age_seconds`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":9648,"byte_end":9663,"line_start":237,"line_end":237,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        max_age_seconds: u64,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":9648,"byte_end":9663,"line_start":237,"line_end":237,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        max_age_seconds: u64,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"_max_age_seconds","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `max_age_seconds`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:237:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m237\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        max_age_seconds: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_max_age_seconds`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `min_progress`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":9678,"byte_end":9690,"line_start":238,"line_end":238,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        min_progress: f64,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":9678,"byte_end":9690,"line_start":238,"line_end":238,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        min_progress: f64,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_min_progress","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `min_progress`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:238:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m238\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        min_progress: f64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_min_progress`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `max_progress`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":9705,"byte_end":9717,"line_start":239,"line_end":239,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        max_progress: f64,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/logs_listener.rs","byte_start":9705,"byte_end":9717,"line_start":239,"line_end":239,"column_start":9,"column_end":21,"is_primary":true,"text":[{"text":"        max_progress: f64,","highlight_start":9,"highlight_end":21}],"label":null,"suggested_replacement":"_max_progress","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `max_progress`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:239:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m239\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        max_progress: f64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_max_progress`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `response`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/websocket_tester.rs","byte_start":3750,"byte_end":3758,"line_start":104,"line_end":104,"column_start":25,"column_end":33,"is_primary":true,"text":[{"text":"        let (ws_stream, response) = match connect_result {","highlight_start":25,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/websocket_tester.rs","byte_start":3750,"byte_end":3758,"line_start":104,"line_end":104,"column_start":25,"column_end":33,"is_primary":true,"text":[{"text":"        let (ws_stream, response) = match connect_result {","highlight_start":25,"highlight_end":33}],"label":null,"suggested_replacement":"_response","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `response`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/websocket_tester.rs:104:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let (ws_stream, response) = match connect_result {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_response`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `refresh_handle`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/enhanced_dashboard.rs","byte_start":3296,"byte_end":3310,"line_start":100,"line_end":100,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"        let refresh_handle = tokio::spawn(async move {","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/enhanced_dashboard.rs","byte_start":3296,"byte_end":3310,"line_start":100,"line_end":100,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"        let refresh_handle = tokio::spawn(async move {","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":"_refresh_handle","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `refresh_handle`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/enhanced_dashboard.rs:100:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let refresh_handle = tokio::spawn(async move {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_refresh_handle`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `is_immediate_candidate`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":92374,"byte_end":92396,"line_start":1955,"line_end":1955,"column_start":13,"column_end":35,"is_primary":true,"text":[{"text":"        let is_immediate_candidate = if let Some(ref aging_pipeline) = self.aging_pipeline {","highlight_start":13,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":92374,"byte_end":92396,"line_start":1955,"line_end":1955,"column_start":13,"column_end":35,"is_primary":true,"text":[{"text":"        let is_immediate_candidate = if let Some(ref aging_pipeline) = self.aging_pipeline {","highlight_start":13,"highlight_end":35}],"label":null,"suggested_replacement":"_is_immediate_candidate","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `is_immediate_candidate`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:1955:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1955\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let is_immediate_candidate = if let Some(ref aging_pipeline) = self.aging_pipeline {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_is_immediate_candidate`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `jito_client` and `pump_fee_recipient` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":826,"byte_end":839,"line_start":17,"line_end":17,"column_start":12,"column_end":25,"is_primary":false,"text":[{"text":"pub struct TradeExecutor {","highlight_start":12,"highlight_end":25}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":990,"byte_end":1001,"line_start":22,"line_end":22,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    jito_client: ProductionJitoClient,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":1083,"byte_end":1101,"line_start":24,"line_end":24,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    pump_fee_recipient: Pubkey,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `jito_client` and `pump_fee_recipient` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/trade_executor.rs:22:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct TradeExecutor {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    jito_client: ProductionJitoClient,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pump_instruction_builder: PumpInstructionBuilder,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pump_fee_recipient: Pubkey,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"methods `get_current_token_price`, `create_pump_buy_instruction`, and `execute_buy_with_jito` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":1798,"byte_end":1816,"line_start":52,"line_end":52,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl TradeExecutor {","highlight_start":1,"highlight_end":19}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":21618,"byte_end":21641,"line_start":461,"line_end":461,"column_start":14,"column_end":37,"is_primary":true,"text":[{"text":"    async fn get_current_token_price(&self, token_mint: &str) -> Result<f64> {","highlight_start":14,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":22011,"byte_end":22038,"line_start":471,"line_end":471,"column_start":14,"column_end":41,"is_primary":true,"text":[{"text":"    async fn create_pump_buy_instruction(&self, token: &LiveTokenData, amount_sol: f64) -> Result<Instruction> {","highlight_start":14,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/trade_executor.rs","byte_start":22783,"byte_end":22804,"line_start":486,"line_end":486,"column_start":14,"column_end":35,"is_primary":true,"text":[{"text":"    async fn execute_buy_with_jito(&mut self, token: &LiveTokenData, position_size: f64) -> Result<BuyResult> {","highlight_start":14,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: methods `get_current_token_price`, `create_pump_buy_instruction`, and `execute_buy_with_jito` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/trade_executor.rs:461:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m52\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl TradeExecutor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m461\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_current_token_price(&self, token_mint: &str) -> Result<f64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m471\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn create_pump_buy_instruction(&self, token: &LiveTokenData, amount_sol: f64) -> Result<Instruction> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m486\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn execute_buy_with_jito(&mut self, token: &LiveTokenData, position_size: f64) -> Result<BuyResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `trading_config` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/position_monitor.rs","byte_start":476,"byte_end":491,"line_start":12,"line_end":12,"column_start":12,"column_end":27,"is_primary":false,"text":[{"text":"pub struct PositionMonitor {","highlight_start":12,"highlight_end":27}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/position_monitor.rs","byte_start":537,"byte_end":551,"line_start":14,"line_end":14,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    trading_config: TradingConfig,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `trading_config` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/position_monitor.rs:14:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct PositionMonitor {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    position_manager: PositionManager,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    trading_config: TradingConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `trading_config` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/sell_logic.rs","byte_start":501,"byte_end":510,"line_start":13,"line_end":13,"column_start":12,"column_end":21,"is_primary":false,"text":[{"text":"pub struct SellLogic {","highlight_start":12,"highlight_end":21}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/sell_logic.rs","byte_start":517,"byte_end":531,"line_start":14,"line_end":14,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    trading_config: TradingConfig,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `trading_config` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/sell_logic.rs:14:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct SellLogic {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    trading_config: TradingConfig,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `chainstack_client`, `helius_client`, `shyft_client`, and `processed_tokens` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":10564,"byte_end":10583,"line_start":276,"line_end":276,"column_start":12,"column_end":31,"is_primary":false,"text":[{"text":"pub struct BondingCurveMonitor {","highlight_start":12,"highlight_end":31}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":10617,"byte_end":10634,"line_start":278,"line_end":278,"column_start":5,"column_end":22,"is_primary":true,"text":[{"text":"    chainstack_client: Option<ChainstackRpcClient>,","highlight_start":5,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":10724,"byte_end":10737,"line_start":280,"line_end":280,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    helius_client: Option<HeliusClient>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":10765,"byte_end":10777,"line_start":281,"line_end":281,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    shyft_client: Option<ShyftClient>,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":11032,"byte_end":11048,"line_start":286,"line_end":286,"column_start":5,"column_end":21,"is_primary":true,"text":[{"text":"    processed_tokens: HashSet<String>,","highlight_start":5,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `chainstack_client`, `helius_client`, `shyft_client`, and `processed_tokens` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:278:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m276\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct BondingCurveMonitor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m277\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    rpc_client: RpcClient,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m278\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    chainstack_client: Option<ChainstackRpcClient>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m279\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    platform_client: Option<ChainstackPlatformClient>,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    helius_client: Option<HeliusClient>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m281\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    shyft_client: Option<ShyftClient>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m286\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    processed_tokens: HashSet<String>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple methods are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":11807,"byte_end":11831,"line_start":311,"line_end":311,"column_start":1,"column_end":25,"is_primary":false,"text":[{"text":"impl BondingCurveMonitor {","highlight_start":1,"highlight_end":25}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":56082,"byte_end":56126,"line_start":1222,"line_end":1222,"column_start":14,"column_end":58,"is_primary":true,"text":[{"text":"    async fn discover_tokens_via_program_accounts_limited(&self) -> Result<Vec<Pubkey>> {","highlight_start":14,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":60885,"byte_end":60920,"line_start":1325,"line_end":1325,"column_start":14,"column_end":49,"is_primary":true,"text":[{"text":"    async fn discover_tokens_via_fallback_method(&self) -> Result<Vec<Pubkey>> {","highlight_start":14,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":61743,"byte_end":61774,"line_start":1343,"line_end":1343,"column_start":14,"column_end":45,"is_primary":true,"text":[{"text":"    async fn fetch_and_extract_mint_from_rpc(&self, bonding_curve_pubkey: &Pubkey) -> Option<Pubkey> {","highlight_start":14,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":62496,"byte_end":62527,"line_start":1359,"line_end":1359,"column_start":14,"column_end":45,"is_primary":true,"text":[{"text":"    async fn get_mint_from_bonding_curve_api(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {","highlight_start":14,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":65024,"byte_end":65056,"line_start":1407,"line_end":1407,"column_start":14,"column_end":46,"is_primary":true,"text":[{"text":"    async fn validate_bonding_curve_freshness(&self, bonding_curve: &Pubkey) -> Result<Option<(Pubkey, bool, f64, bool)>> {","highlight_start":14,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":66936,"byte_end":66962,"line_start":1444,"line_end":1444,"column_start":14,"column_end":40,"is_primary":true,"text":[{"text":"    async fn derive_mint_via_pda_search(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {","highlight_start":14,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":73842,"byte_end":73878,"line_start":1579,"line_end":1579,"column_start":8,"column_end":44,"is_primary":true,"text":[{"text":"    fn extract_mint_from_bonding_curve_data(&self, account_data: &[u8]) -> Option<Pubkey> {","highlight_start":8,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":74382,"byte_end":74415,"line_start":1588,"line_end":1588,"column_start":14,"column_end":47,"is_primary":true,"text":[{"text":"    async fn try_pump_fun_bonding_curve_lookup(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {","highlight_start":14,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":78167,"byte_end":78197,"line_start":1664,"line_end":1664,"column_start":8,"column_end":38,"is_primary":true,"text":[{"text":"    fn derive_mint_from_bonding_curve(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {","highlight_start":8,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":78908,"byte_end":78940,"line_start":1679,"line_end":1679,"column_start":14,"column_end":46,"is_primary":true,"text":[{"text":"    async fn get_token_metadata_for_age_check(&self, mint: &Pubkey) -> Result<TokenAgeData> {","highlight_start":14,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":91468,"byte_end":91496,"line_start":1940,"line_end":1940,"column_start":8,"column_end":36,"is_primary":true,"text":[{"text":"    fn derive_bonding_curve_address(&self, mint: &Pubkey) -> Pubkey {","highlight_start":8,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration/bonding_curve_monitor.rs","byte_start":91792,"byte_end":91831,"line_start":1946,"line_end":1946,"column_start":8,"column_end":47,"is_primary":true,"text":[{"text":"    fn derive_associated_bonding_curve_address(&self, mint: &Pubkey) -> Pubkey {","highlight_start":8,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple methods are never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration/bonding_curve_monitor.rs:1222:14\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m311\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl BondingCurveMonitor {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1222\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn discover_tokens_via_program_accounts_limited(&self) -> Result<Vec<Pubkey>> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1325\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn discover_tokens_via_fallback_method(&self) -> Result<Vec<Pubkey>> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1343\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn fetch_and_extract_mint_from_rpc(&self, bonding_curve_pubkey: &Pubkey) -> Option<Pubkey> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_mint_from_bonding_curve_api(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1407\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn validate_bonding_curve_freshness(&self, bonding_curve: &Pubkey) -> Result<Option<(Pubkey, bool, f64, bool)>> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1444\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn derive_mint_via_pda_search(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1579\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn extract_mint_from_bonding_curve_data(&self, account_data: &[u8]) -> Option<Pubkey> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1588\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn try_pump_fun_bonding_curve_lookup(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1664\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn derive_mint_from_bonding_curve(&self, bonding_curve: &Pubkey) -> Option<Pubkey> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1679\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_token_metadata_for_age_check(&self, mint: &Pubkey) -> Result<TokenAgeData> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1940\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn derive_bonding_curve_address(&self, mint: &Pubkey) -> Pubkey {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1946\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn derive_associated_bonding_curve_address(&self, mint: &Pubkey) -> Pubkey {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple fields are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":8488,"byte_end":8505,"line_start":240,"line_end":240,"column_start":12,"column_end":29,"is_primary":false,"text":[{"text":"pub struct LivePumpFunClient {","highlight_start":12,"highlight_end":29}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":8539,"byte_end":8550,"line_start":242,"line_end":242,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    http_client: Client,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":9232,"byte_end":9243,"line_start":266,"line_end":266,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    jito_client: ProductionJitoClient,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":9410,"byte_end":9425,"line_start":271,"line_end":271,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    pump_api_client: PumpApiClient,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":9471,"byte_end":9486,"line_start":274,"line_end":274,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    pump_program_id: Pubkey,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":9500,"byte_end":9519,"line_start":275,"line_end":275,"column_start":5,"column_end":24,"is_primary":true,"text":[{"text":"    pump_global_account: Pubkey,","highlight_start":5,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":9533,"byte_end":9551,"line_start":276,"line_end":276,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"    pump_fee_recipient: Pubkey,","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":10094,"byte_end":10104,"line_start":293,"line_end":293,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    sell_logic: crate::live_pump_integration::SellLogic,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple fields are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:242:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m240\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct LivePumpFunClient {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m241\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    rpc_client: RpcClient,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m242\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    http_client: Client,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    jito_client: ProductionJitoClient,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m271\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pump_api_client: PumpApiClient,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pump_program_id: Pubkey,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pump_global_account: Pubkey,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m276\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pump_fee_recipient: Pubkey,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sell_logic: crate::live_pump_integration::SellLogic,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/live_pump_integration_main.rs","byte_start":12193,"byte_end":12215,"line_start":356,"line_end":356,"column_start":1,"column_end":23,"is_primary":false,"text":[{"text":"impl LivePumpFunClient {","highlight_start":1,"highlight_end":23}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":49779,"byte_end":49807,"line_start":1036,"line_end":1036,"column_start":8,"column_end":36,"is_primary":true,"text":[{"text":"    fn parse_pumpfun_token_creation(json_value: &serde_json::Value) -> Result<TokenCreationEvent> {","highlight_start":8,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":52166,"byte_end":52191,"line_start":1117,"line_end":1117,"column_start":8,"column_end":33,"is_primary":true,"text":[{"text":"    fn parse_pumpfun_trade_event(json_value: &serde_json::Value) -> Result<TradeEvent> {","highlight_start":8,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":53591,"byte_end":53607,"line_start":1169,"line_end":1169,"column_start":14,"column_end":30,"is_primary":true,"text":[{"text":"    async fn handle_new_token(&mut self, token_event: TokenCreationEvent) -> Result<()> {","highlight_start":14,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":71370,"byte_end":71386,"line_start":1543,"line_end":1543,"column_start":14,"column_end":30,"is_primary":true,"text":[{"text":"    async fn fetch_token_data(&self, mint: &str) -> Result<LiveTokenData> {","highlight_start":14,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":74082,"byte_end":74112,"line_start":1599,"line_end":1599,"column_start":14,"column_end":44,"is_primary":true,"text":[{"text":"    async fn execute_buy_with_priority_fees(&mut self, token: &LiveTokenData, position_size: f64) -> Result<(u64, f64, f64)> {","highlight_start":14,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":78649,"byte_end":78670,"line_start":1692,"line_end":1692,"column_start":14,"column_end":35,"is_primary":true,"text":[{"text":"    async fn execute_buy_with_jito(&mut self, token: &LiveTokenData, position_size: f64) -> Result<(u64, f64, f64)> {","highlight_start":14,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":79861,"byte_end":79903,"line_start":1719,"line_end":1719,"column_start":14,"column_end":56,"is_primary":true,"text":[{"text":"    async fn execute_sell_with_priority_fees_deprecated(&mut self, _position: &str) -> Result<TransactionResult> {","highlight_start":14,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":80305,"byte_end":80338,"line_start":1726,"line_end":1726,"column_start":14,"column_end":47,"is_primary":true,"text":[{"text":"    async fn execute_sell_with_jito_deprecated(&mut self, _token_mint: &str) -> Result<TransactionResult> {","highlight_start":14,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":80718,"byte_end":80745,"line_start":1733,"line_end":1733,"column_start":14,"column_end":41,"is_primary":true,"text":[{"text":"    async fn create_pump_buy_instruction(&self, token: &LiveTokenData, amount_sol: f64) -> Result<Instruction> {","highlight_start":14,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":81429,"byte_end":81468,"line_start":1747,"line_end":1747,"column_start":14,"column_end":53,"is_primary":true,"text":[{"text":"    async fn create_pump_sell_instruction_deprecated(&self, _token_mint: &str) -> Result<Instruction> {","highlight_start":14,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":81877,"byte_end":81910,"line_start":1754,"line_end":1754,"column_start":14,"column_end":47,"is_primary":true,"text":[{"text":"    async fn update_position_prices_deprecated(&mut self, _trade_event: &TradeEvent) {","highlight_start":14,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":82229,"byte_end":82260,"line_start":1760,"line_end":1760,"column_start":14,"column_end":45,"is_primary":true,"text":[{"text":"    async fn check_position_exits_deprecated(&mut self) -> Result<()> {","highlight_start":14,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":82564,"byte_end":82595,"line_start":1767,"line_end":1767,"column_start":8,"column_end":39,"is_primary":true,"text":[{"text":"    fn should_exit_position_deprecated(&self, _token_mint: &str) -> (bool, String) {","highlight_start":8,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":84836,"byte_end":84865,"line_start":1813,"line_end":1813,"column_start":14,"column_end":43,"is_primary":true,"text":[{"text":"    async fn execute_sell_order_deprecated(&mut self, _token_mint: &str) -> Result<()> {","highlight_start":14,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":85235,"byte_end":85253,"line_start":1820,"line_end":1820,"column_start":14,"column_end":32,"is_primary":true,"text":[{"text":"    async fn get_wallet_balance(&self) -> Result<f64> {","highlight_start":14,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":85594,"byte_end":85626,"line_start":1828,"line_end":1828,"column_start":8,"column_end":40,"is_primary":true,"text":[{"text":"    fn determine_exit_reason_deprecated(&self, _token_mint: &str) -> String {","highlight_start":8,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":95560,"byte_end":95580,"line_start":2021,"line_end":2021,"column_start":14,"column_end":34,"is_primary":true,"text":[{"text":"    async fn scan_existing_tokens(&mut self) -> Result<()> {","highlight_start":14,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":102469,"byte_end":102494,"line_start":2147,"line_end":2147,"column_start":8,"column_end":33,"is_primary":true,"text":[{"text":"    fn convert_api_to_live_token(&self, api_data: &crate::pump_api_client::PumpApiTokenData) -> Result<LiveTokenData> {","highlight_start":8,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":104518,"byte_end":104534,"line_start":2187,"line_end":2187,"column_start":14,"column_end":30,"is_primary":true,"text":[{"text":"    async fn check_api_health(&self) -> Result<bool> {","highlight_start":14,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":115914,"byte_end":115943,"line_start":2428,"line_end":2428,"column_start":14,"column_end":43,"is_primary":true,"text":[{"text":"    async fn validate_graduation_candidate(&self, candidate: &LiveTokenData) -> bool {","highlight_start":14,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/live_pump_integration_main.rs","byte_start":118128,"byte_end":118153,"line_start":2478,"line_end":2478,"column_start":14,"column_end":39,"is_primary":true,"text":[{"text":"    async fn create_and_track_position(&mut self, token: &LiveTokenData, buy_result: &crate::live_pump_integration::BuyResult) -> Result<()> {","highlight_start":14,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: multiple associated items are never used\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/live_pump_integration_main.rs:1036:8\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m356\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl LivePumpFunClient {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1036\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn parse_pumpfun_token_creation(json_value: &serde_json::Value) -> Result<TokenCreationEvent> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1117\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn parse_pumpfun_trade_event(json_value: &serde_json::Value) -> Result<TradeEvent> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1169\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn handle_new_token(&mut self, token_event: TokenCreationEvent) -> Result<()> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1543\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn fetch_token_data(&self, mint: &str) -> Result<LiveTokenData> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1599\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn execute_buy_with_priority_fees(&mut self, token: &LiveTokenData, position_size: f64) -> Result<(u64, f64, f64)> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1692\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn execute_buy_with_jito(&mut self, token: &LiveTokenData, position_size: f64) -> Result<(u64, f64, f64)> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1719\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn execute_sell_with_priority_fees_deprecated(&mut self, _position: &str) -> Result<TransactionResult> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1726\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn execute_sell_with_jito_deprecated(&mut self, _token_mint: &str) -> Result<TransactionResult> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1733\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn create_pump_buy_instruction(&self, token: &LiveTokenData, amount_sol: f64) -> Result<Instruction> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1747\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn create_pump_sell_instruction_deprecated(&self, _token_mint: &str) -> Result<Instruction> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1754\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn update_position_prices_deprecated(&mut self, _trade_event: &TradeEvent) {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1760\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_position_exits_deprecated(&mut self) -> Result<()> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1767\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn should_exit_position_deprecated(&self, _token_mint: &str) -> (bool, String) {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1813\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn execute_sell_order_deprecated(&mut self, _token_mint: &str) -> Result<()> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1820\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_wallet_balance(&self) -> Result<f64> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1828\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn determine_exit_reason_deprecated(&self, _token_mint: &str) -> String {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2021\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn scan_existing_tokens(&mut self) -> Result<()> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn convert_api_to_live_token(&self, api_data: &crate::pump_api_client::PumpApiTokenData) -> Result<LiveTokenData> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_api_health(&self) -> Result<bool> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2428\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn validate_graduation_candidate(&self, candidate: &LiveTokenData) -> bool {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2478\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn create_and_track_position(&mut self, token: &LiveTokenData, buy_result: &crate::live_pump_integration::BuyResult) -> Result<()> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `ev_calculation_window` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/performance_dashboard.rs","byte_start":2193,"byte_end":2213,"line_start":73,"line_end":73,"column_start":12,"column_end":32,"is_primary":false,"text":[{"text":"pub struct PerformanceDashboard {","highlight_start":12,"highlight_end":32}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/performance_dashboard.rs","byte_start":2524,"byte_end":2545,"line_start":85,"line_end":85,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"    ev_calculation_window: usize,","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `ev_calculation_window` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/performance_dashboard.rs:85:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct PerformanceDashboard {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ev_calculation_window: usize,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `total_positions_completed` and `total_positions_failed` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/position_manager.rs","byte_start":14684,"byte_end":14699,"line_start":364,"line_end":364,"column_start":12,"column_end":27,"is_primary":false,"text":[{"text":"pub struct PositionManager {","highlight_start":12,"highlight_end":27}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/position_manager.rs","byte_start":15040,"byte_end":15065,"line_start":373,"line_end":373,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"    total_positions_completed: u64,","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/position_manager.rs","byte_start":15076,"byte_end":15098,"line_start":374,"line_end":374,"column_start":5,"column_end":27,"is_primary":true,"text":[{"text":"    total_positions_failed: u64,","highlight_start":5,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `total_positions_completed` and `total_positions_failed` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/position_manager.rs:373:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m364\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct PositionManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m373\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    total_positions_completed: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m374\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    total_positions_failed: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `http_client` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/chainstack_client.rs","byte_start":587,"byte_end":606,"line_start":21,"line_end":21,"column_start":12,"column_end":31,"is_primary":false,"text":[{"text":"pub struct ChainstackRpcClient {","highlight_start":12,"highlight_end":31}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/chainstack_client.rs","byte_start":726,"byte_end":737,"line_start":27,"line_end":27,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    http_client: reqwest::Client,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `http_client` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/chainstack_client.rs:27:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ChainstackRpcClient {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    http_client: reqwest::Client,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `get_program_accounts_authenticated` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/chainstack_client.rs","byte_start":793,"byte_end":817,"line_start":31,"line_end":31,"column_start":1,"column_end":25,"is_primary":false,"text":[{"text":"impl ChainstackRpcClient {","highlight_start":1,"highlight_end":25}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/chainstack_client.rs","byte_start":4579,"byte_end":4613,"line_start":122,"line_end":122,"column_start":14,"column_end":48,"is_primary":true,"text":[{"text":"    async fn get_program_accounts_authenticated(&self, program_id: &Pubkey) -> Result<Vec<(Pubkey, Account)>, anyhow::Error> {","highlight_start":14,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: method `get_program_accounts_authenticated` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/chainstack_client.rs:122:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m31\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ChainstackRpcClient {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_program_accounts_authenticated(&self, program_id: &Pubkey) -> Result<Vec<(Pubkey, Account)>, anyhow::Error> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `pump_program` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_event_processor.rs","byte_start":267,"byte_end":285,"line_start":8,"line_end":8,"column_start":12,"column_end":30,"is_primary":false,"text":[{"text":"pub struct LogsEventProcessor {","highlight_start":12,"highlight_end":30}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/logs_event_processor.rs","byte_start":292,"byte_end":304,"line_start":9,"line_end":9,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    pump_program: Pubkey,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `pump_program` is never read\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_event_processor.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct LogsEventProcessor {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pump_program: Pubkey,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `ping_interval` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/logs_listener.rs","byte_start":484,"byte_end":496,"line_start":13,"line_end":13,"column_start":12,"column_end":24,"is_primary":false,"text":[{"text":"pub struct LogsListener {","highlight_start":12,"highlight_end":24}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/logs_listener.rs","byte_start":596,"byte_end":609,"line_start":17,"line_end":17,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    ping_interval: Duration,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `ping_interval` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/logs_listener.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct LogsListener {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ping_interval: Duration,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `check_bonding_curve_progress` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/token_aging_pipeline.rs","byte_start":4455,"byte_end":4478,"line_start":125,"line_end":125,"column_start":1,"column_end":24,"is_primary":false,"text":[{"text":"impl TokenAgingPipeline {","highlight_start":1,"highlight_end":24}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/token_aging_pipeline.rs","byte_start":26961,"byte_end":26989,"line_start":558,"line_end":558,"column_start":14,"column_end":42,"is_primary":true,"text":[{"text":"    async fn check_bonding_curve_progress(&self, bonding_curve: &Pubkey) -> Result<Option<(f64, f64, bool)>> {","highlight_start":14,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: method `check_bonding_curve_progress` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/token_aging_pipeline.rs:558:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl TokenAgingPipeline {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m558\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_bonding_curve_progress(&self, bonding_curve: &Pubkey) -> Result<Option<(f64, f64, bool)>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"use of `async fn` in public traits is discouraged as auto trait bounds cannot be specified","code":{"code":"async_fn_in_trait","explanation":null},"level":"warning","spans":[{"file_name":"src/mev_protection.rs","byte_start":11839,"byte_end":11844,"line_start":315,"line_end":315,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    async fn execute_protected_trade(","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"you can suppress this lint if you plan to use the trait only in your own code, or do not care about auto traits like `Send` on the `Future`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(async_fn_in_trait)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"you can alternatively desugar to a normal `fn` that returns `impl Future` and add any desired bounds such as `Send`, but these cannot be relaxed without a breaking API change","code":null,"level":"help","spans":[{"file_name":"src/mev_protection.rs","byte_start":11960,"byte_end":11960,"line_start":319,"line_end":319,"column_start":10,"column_end":10,"is_primary":true,"text":[{"text":"    ) -> Result<MevProtectionResult>;","highlight_start":10,"highlight_end":10}],"label":null,"suggested_replacement":"impl std::future::Future<Output = ","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/mev_protection.rs","byte_start":11987,"byte_end":11987,"line_start":319,"line_end":319,"column_start":37,"column_end":37,"is_primary":true,"text":[{"text":"    ) -> Result<MevProtectionResult>;","highlight_start":37,"highlight_end":37}],"label":null,"suggested_replacement":"> + Send","suggestion_applicability":"MaybeIncorrect","expansion":null},{"file_name":"src/mev_protection.rs","byte_start":11839,"byte_end":11845,"line_start":315,"line_end":315,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    async fn execute_protected_trade(","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: use of `async fn` in public traits is discouraged as auto trait bounds cannot be specified\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/mev_protection.rs:315:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m315\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn execute_protected_trade(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: you can suppress this lint if you plan to use the trait only in your own code, or do not care about auto traits like `Send` on the `Future`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(async_fn_in_trait)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: you can alternatively desugar to a normal `fn` that returns `impl Future` and add any desired bounds such as `Send`, but these cannot be relaxed without a breaking API change\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m315\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    fn execute_protected_trade(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m316\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         &mut self,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m317\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         transaction: Transaction,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m318\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         payer: &Keypair,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m    ) -> \u001b[0m\u001b[0m\u001b[38;5;10mimpl std::future::Future<Output = \u001b[0m\u001b[0mResult<MevProtectionResult>\u001b[0m\u001b[0m\u001b[38;5;10m> + Send\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"72 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 72 warnings emitted\u001b[0m\n\n"}
