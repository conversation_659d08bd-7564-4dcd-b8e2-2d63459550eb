# Bonding Curve Velocity Implementation

## Overview

The bonding curve velocity feature has been fully implemented to provide quantitative price movement analysis for token selection. This implementation calculates price velocity (percentage change per minute) from price history and integrates it into the scoring system with configurable environment variables.

**⚠️ IMPORTANT UPDATE**: Velocity calculations have been changed from per-hour to per-minute to align with the bot's short-term trading strategy (typically holding tokens for minutes, not hours).

## Environment Variables

### Core Velocity Configuration

- **`PUMP_MIN_VELOCITY`** (default: `0.083`)
  - Minimum required velocity in %/minute format (0.083 = 0.083%/minute ≈ 5%/hour)
  - Tokens below this threshold are filtered out
  - Example: `PUMP_MIN_VELOCITY=0.167` for 0.167%/minute (≈10%/hour) minimum

- **`PUMP_VELOCITY_WEIGHT`** (default: `0.4`)
  - Weight of velocity in momentum score (0.0-1.0)
  - Higher values prioritize velocity over trading activity
  - Example: `PUMP_VELOCITY_WEIGHT=0.6` for 60% velocity, 40% trading activity

- **`PUMP_VELOCITY_TIMEFRAME_MINUTES`** (default: `30`)
  - Timeframe for velocity calculation in minutes
  - ≤30 uses short-term velocity (last 30 minutes)
  - >30 uses full price history velocity
  - Example: `PUMP_VELOCITY_TIMEFRAME_MINUTES=60` for 1-hour analysis

## Velocity Calculation Methods

### 1. Full Price History Velocity
```rust
velocity = (price_change_percent / time_minutes)
```
- Uses oldest to newest price points
- Provides overall trend analysis
- Better for longer-term momentum assessment

### 2. Short-Term Velocity (≤30 minutes)
```rust
velocity = (recent_price_change_percent / time_minutes)
```
- Uses only last 30 minutes of data
- More responsive to immediate price movements
- Better for real-time trading decisions

## Velocity Classifications

- **EXTREME**: ≥2.0%/minute (≥120%/hour)
- **HIGH**: 1.0-2.0%/minute (60-120%/hour)
- **MODERATE**: 0.5-1.0%/minute (30-60%/hour)
- **LOW**: 0.2-0.5%/minute (12-30%/hour)
- **MINIMAL**: <0.2%/minute (<12%/hour)

## Integration with Scoring System

### Momentum Score Calculation
```rust
momentum_score = (velocity_score * velocity_weight) + (trading_activity_score * (1 - velocity_weight))
```

### Velocity Score Normalization
- Positive velocity: `(velocity / 2.0).min(1.0)` (0-2%/minute → 0-1.0 score)
- Negative velocity: `((velocity.abs() / 4.0) * 0.3).min(0.3)` (reduced score for dip opportunities)

### Filtering Logic
- Tokens must meet `PUMP_MIN_VELOCITY` threshold to pass velocity filter
- Failed velocity filter results in immediate rejection regardless of other scores

## Logging and Debugging

### Velocity Statistics
- Printed at start of each scan cycle
- Shows top 5 tokens by velocity
- Displays configuration and compliance status

### Per-Token Analysis
```
🚀 VELOCITY ANALYSIS for TK1234:
   📈 Velocity: 0.258%/minute (MODERATE)
   ✅ Meets requirement: true (min: 0.083%/minute)
   📊 Velocity score: 0.129, Trading score: 0.750, Combined: 0.423
   🔍 VELOCITY DEBUG: 12 price points over 45.2 min | Full: 0.258%/min | Short: 0.303%/min | Class: MODERATE | Timeframe: 30min
```

## Testing

### Built-in Test Function
```rust
bonding_curve_monitor.test_velocity_calculations()?;
```
- Creates simulated price history
- Tests all velocity calculation methods
- Validates scoring integration
- Provides comprehensive output for verification

## Usage Examples

### Conservative Setup (Low Velocity Threshold)
```bash
export PUMP_MIN_VELOCITY=0.033         # 0.033%/minute (≈2%/hour) minimum
export PUMP_VELOCITY_WEIGHT=0.3        # 30% velocity weight
export PUMP_VELOCITY_TIMEFRAME_MINUTES=60  # 1-hour analysis
```

### Aggressive Setup (High Velocity Threshold)
```bash
export PUMP_MIN_VELOCITY=0.25          # 0.25%/minute (≈15%/hour) minimum
export PUMP_VELOCITY_WEIGHT=0.7        # 70% velocity weight
export PUMP_VELOCITY_TIMEFRAME_MINUTES=15  # 15-minute analysis
```

### Balanced Setup (Default)
```bash
export PUMP_MIN_VELOCITY=0.083         # 0.083%/minute (≈5%/hour) minimum
export PUMP_VELOCITY_WEIGHT=0.4        # 40% velocity weight
export PUMP_VELOCITY_TIMEFRAME_MINUTES=30  # 30-minute analysis
```

## Implementation Details

### Price History Management
- Automatically tracks price for each analyzed token
- Maintains up to 100 price points per token
- Removes oldest points to prevent memory bloat
- Updates on every token analysis cycle

### Performance Considerations
- Velocity calculations are O(n) where n is price history length
- Memory usage scales with number of tracked tokens
- Price history is capped to prevent excessive memory usage
- Calculations are performed only when needed

## Integration Points

1. **Token Discovery**: Price tracking starts when token is first analyzed
2. **Scoring System**: Velocity integrated into momentum score calculation
3. **Filtering**: Velocity threshold applied before final scoring
4. **Logging**: Comprehensive velocity information in all relevant log outputs

## Troubleshooting

### Common Issues

1. **No velocity data**: Token needs multiple price points over time
2. **Velocity too low**: Adjust `PUMP_MIN_VELOCITY` or wait for price movement
3. **Inconsistent results**: Check `PUMP_VELOCITY_TIMEFRAME_MINUTES` setting

### Debug Commands
- Monitor velocity statistics in scan output
- Use test function to verify calculations
- Check individual token velocity debug information
